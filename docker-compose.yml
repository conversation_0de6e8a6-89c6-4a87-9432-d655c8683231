version: "3.8"

services:
  jobsearch-app:
    build: .
    container_name: jobsearch-flask-app
    ports:
      - "5000:5000"
    env_file:
      - .env
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///instance/jobsearch.db
    volumes:
      - jobsearch_data:/app/instance
    restart: unless-stopped
    networks:
      - jobsearch-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  jobsearch_data:
    driver: local

networks:
  jobsearch-network:
    driver: bridge
